from flask import Flask, render_template, jsonify, request, send_file, make_response
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import pandas as pd
import io
import xlsxwriter
from werkzeug.utils import secure_filename
import os
from flask.cli import with_appcontext
import click
from io import BytesIO
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch


ALLOWED_IPS = [
    "************",  # Your colleague's IP
    "127.0.0.1",  # localhost
    # Add more IPs as needed
]

app = Flask(__name__)
app.config["SQLALCHEMY_DATABASE_URI"] = "sqlite:///operations.db"
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
db = SQLAlchemy(app)


# Models
class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    employees = db.relationship("Employee", backref="department", lazy=True)


class Tool(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    access_rights = db.relationship("EmployeeToolAccess", backref="tool", lazy=True)


class AccessRight(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    current_access_assignments = db.relationship(
        "EmployeeToolAccess",
        backref="current_access_right",
        foreign_keys="EmployeeToolAccess.current_access_id",
    )
    ideal_access_assignments = db.relationship(
        "EmployeeToolAccess",
        backref="ideal_access_right",
        foreign_keys="EmployeeToolAccess.ideal_access_id",
    )


class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(100))
    level = db.Column(db.String(50))
    department_id = db.Column(db.Integer, db.ForeignKey("department.id"))
    comments = db.Column(db.Text)
    tool_access = db.relationship("EmployeeToolAccess", backref="employee", lazy=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )


class EmployeeToolAccess(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey("employee.id"), nullable=False)
    tool_id = db.Column(db.Integer, db.ForeignKey("tool.id"), nullable=False)
    current_access_id = db.Column(db.Integer, db.ForeignKey("access_right.id"))
    ideal_access_id = db.Column(db.Integer, db.ForeignKey("access_right.id"))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )


# Routes
@app.route("/")
def home():
    return render_template("home.html")


@app.route("/accesses")
def accesses_list():
    return render_template("accesses/list.html")


@app.route("/accesses/manage")
def accesses_manage():
    return render_template("accesses/manage.html")


@app.route("/accesses/dashboard")
def accesses_dashboard():
    return render_template("accesses/dashboard.html")


# API Routes
@app.route("/api/employees", methods=["GET"])
def get_employees():
    try:
        # Get search query from request parameters
        search_query = request.args.get("search", "").lower()

        # Base query
        query = Employee.query.order_by(Employee.id.desc())

        # Apply search if provided
        if search_query:
            query = query.filter(
                db.or_(
                    Employee.name.ilike(f"%{search_query}%"),
                    Employee.role.ilike(f"%{search_query}%"),
                    Employee.level.ilike(f"%{search_query}%"),
                    Employee.comments.ilike(f"%{search_query}%"),
                    Department.name.ilike(f"%{search_query}%"),
                )
            ).join(Employee.department, isouter=True)

        employees = query.all()
        employee_data = []

        for employee in employees:
            base_data = {
                "id": employee.id,
                "name": employee.name,
                "role": employee.role,
                "department_id": employee.department_id,
                "department": employee.department.name if employee.department else None,
                "level": employee.level,
                "comments": employee.comments,
            }

            if not employee.tool_access:
                employee_data.append(
                    {
                        **base_data,
                        "tool": None,
                        "current_access": None,
                        "ideal_access": None,
                    }
                )
            else:
                for access in employee.tool_access:
                    employee_data.append(
                        {
                            **base_data,
                            "tool": (
                                {"id": access.tool_id, "name": access.tool.name}
                                if access.tool_id
                                else None
                            ),
                            "current_access": (
                                {
                                    "id": access.current_access_id,
                                    "name": access.current_access_right.name,
                                }
                                if access.current_access_id
                                else None
                            ),
                            "ideal_access": (
                                {
                                    "id": access.ideal_access_id,
                                    "name": access.ideal_access_right.name,
                                }
                                if access.ideal_access_id
                                else None
                            ),
                        }
                    )

        return jsonify(employee_data)
    except Exception as e:
        print(f"Error in get_employees: {str(e)}")  # For debugging
        return jsonify({"error": str(e)}), 500


@app.route("/api/employees", methods=["POST"])
def create_employee():
    try:
        data = request.json
        with db.session.begin():
            employee = Employee(
                name=data["name"],
                role=data.get("role"),
                department_id=data.get("department_id"),
                level=data.get("level"),
                comments=data.get("comments"),
            )
            db.session.add(employee)
            db.session.flush()  # This ensures we get the employee ID

            # Add tool access
            for tool_access in data.get("tool_access", []):
                access = EmployeeToolAccess(
                    employee_id=employee.id,
                    tool_id=tool_access["tool_id"],
                    current_access_id=tool_access.get("current_access_id"),
                    ideal_access_id=tool_access.get("ideal_access_id"),
                )
                db.session.add(access)

        # Get the updated employee data to return
        return jsonify({"message": "Employee created successfully", "id": employee.id})
    except Exception as e:
        db.session.rollback()
        print(f"Error creating employee: {str(e)}")  # For debugging
        return jsonify({"error": str(e)}), 500


@app.route("/api/employees/<int:id>", methods=["PUT"])
def update_employee(id):
    employee = Employee.query.get_or_404(id)
    data = request.json

    employee.name = data["name"]
    employee.role = data.get("role")
    employee.department_id = data.get("department_id")
    employee.level = data.get("level")
    employee.comments = data.get("comments")

    # Update tool access
    EmployeeToolAccess.query.filter_by(employee_id=id).delete()
    for tool_access in data.get("tool_access", []):
        access = EmployeeToolAccess(
            tool_id=tool_access["tool_id"],
            current_access_id=tool_access.get("current_access_id"),
            ideal_access_id=tool_access.get("ideal_access_id"),
        )
        employee.tool_access.append(access)

    db.session.commit()
    return jsonify({"message": "Employee updated successfully"})


@app.route("/api/employees/<int:id>", methods=["DELETE"])
def delete_employee(id):
    try:
        # Start a transaction
        with db.session.begin():
            # First delete all tool access records for this employee
            EmployeeToolAccess.query.filter_by(employee_id=id).delete()

            # Then delete the employee
            employee = Employee.query.get_or_404(id)
            db.session.delete(employee)

        return jsonify({"message": "Employee deleted successfully"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500


@app.route("/api/employees/export/<format>")
def export_employees(format):
    try:
        employees = Employee.query.all()
        data = []

        # Prepare headers
        headers = [
            "Employee Name",
            "Role",
            "Department",
            "Level",
            "Tool",
            "Current Access",
            "Ideal Access",
            "Comments",
        ]
        data.append(headers)  # Add headers as first row

        # Prepare data rows
        for employee in employees:
            if not employee.tool_access:
                # Employee with no tools
                data.append(
                    [
                        employee.name,
                        employee.role or "",
                        employee.department.name if employee.department else "",
                        employee.level or "",
                        "",  # Tool
                        "",  # Current Access
                        "",  # Ideal Access
                        employee.comments or "",
                    ]
                )
            else:
                # Employee with tools
                for access in employee.tool_access:
                    data.append(
                        [
                            employee.name,
                            employee.role or "",
                            employee.department.name if employee.department else "",
                            employee.level or "",
                            access.tool.name if access.tool else "",
                            (
                                access.current_access_right.name
                                if access.current_access_right
                                else ""
                            ),
                            (
                                access.ideal_access_right.name
                                if access.ideal_access_right
                                else ""
                            ),
                            employee.comments or "",
                        ]
                    )

        if format == "pdf":
            buffer = BytesIO()

            # Create the PDF document using landscape orientation and adjusted margins
            doc = SimpleDocTemplate(
                buffer,
                pagesize=landscape(A4),
                rightMargin=20,  # Reduced margins
                leftMargin=20,
                topMargin=20,
                bottomMargin=20,
            )

            story = []
            styles = getSampleStyleSheet()

            # Adjust title style
            title_style = ParagraphStyle(
                "CustomTitle",
                parent=styles["Heading1"],
                fontSize=16,  # Reduced font size
                spaceAfter=20,
                alignment=1,
            )
            title = Paragraph("Employee Access Report", title_style)
            story.append(title)

            # Calculate column widths based on content
            table_style = TableStyle(
                [
                    # Header styling
                    ("BACKGROUND", (0, 0), (-1, 0), colors.grey),
                    ("TEXTCOLOR", (0, 0), (-1, 0), colors.whitesmoke),
                    ("ALIGN", (0, 0), (-1, 0), "CENTER"),
                    ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                    ("FONTSIZE", (0, 0), (-1, 0), 10),  # Reduced font size
                    ("BOTTOMPADDING", (0, 0), (-1, 0), 8),
                    # Data styling
                    ("BACKGROUND", (0, 1), (-1, -1), colors.white),
                    ("TEXTCOLOR", (0, 1), (-1, -1), colors.black),
                    ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                    ("FONTNAME", (0, 1), (-1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 1), (-1, -1), 8),  # Reduced font size
                    ("TOPPADDING", (0, 1), (-1, -1), 4),
                    ("BOTTOMPADDING", (0, 1), (-1, -1), 4),
                    ("GRID", (0, 0), (-1, -1), 0.5, colors.black),  # Thinner grid lines
                ]
            )

            # Calculate available width
            available_width = landscape(A4)[0] - doc.leftMargin - doc.rightMargin

            # Define column widths proportionally
            col_widths = [
                available_width * 0.15,  # Employee Name (15%)
                available_width * 0.12,  # Role (12%)
                available_width * 0.12,  # Department (12%)
                available_width * 0.08,  # Level (8%)
                available_width * 0.12,  # Tool (12%)
                available_width * 0.12,  # Current Access (12%)
                available_width * 0.12,  # Ideal Access (12%)
                available_width * 0.17,  # Comments (17%)
            ]

            # Create table with specific column widths
            table = Table(data, colWidths=col_widths)
            table.setStyle(table_style)

            # Add automatic word wrapping for long content
            wrapped_data = []
            for row in data:
                wrapped_row = []
                for i, cell in enumerate(row):
                    if cell:
                        p = Paragraph(str(cell), styles["Normal"])
                        wrapped_row.append(p)
                    else:
                        wrapped_row.append("")
                wrapped_data.append(wrapped_row)

            # Create final table with wrapped content
            final_table = Table(wrapped_data, colWidths=col_widths)
            final_table.setStyle(table_style)

            story.append(final_table)

            # Build PDF
            doc.build(story)
            buffer.seek(0)

            response = make_response(buffer.getvalue())
            response.headers["Content-Type"] = "application/pdf"
            response.headers["Content-Disposition"] = (
                "attachment; filename=employees.pdf"
            )
            return response

        elif format == "csv":
            output = BytesIO()
            df = pd.DataFrame(data[1:], columns=headers)  # Skip headers row for pandas
            df.to_csv(output, index=False, encoding="utf-8")
            output.seek(0)
            return send_file(
                output,
                mimetype="text/csv",
                as_attachment=True,
                download_name="employees.csv",
            )

        elif format == "excel":
            output = BytesIO()
            df = pd.DataFrame(data[1:], columns=headers)  # Skip headers row for pandas
            with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
                df.to_excel(writer, index=False, sheet_name="Employees")
            output.seek(0)
            return send_file(
                output,
                mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                as_attachment=True,
                download_name="employees.xlsx",
            )

        else:
            return jsonify({"error": "Unsupported format"}), 400

    except Exception as e:
        print(f"Export error: {str(e)}")  # For debugging
        return jsonify({"error": str(e)}), 500


@app.route("/api/template/download", methods=["GET"])
def download_template():
    df = pd.DataFrame(
        columns=[
            "Employee Name",
            "Role",
            "Department",
            "Level",
            "Tool",
            "Current Access",
            "Ideal Access",
            "Comments",
        ]
    )

    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        df.to_excel(writer, index=False)
        worksheet = writer.sheets["Sheet1"]
        worksheet.write("A2", "Example: John Doe")
        worksheet.write("B2", "Example: Developer")
        worksheet.write("C2", "Example: IT")
    output.seek(0)

    return send_file(
        output,
        mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        as_attachment=True,
        download_name="access_template.xlsx",
    )


@app.route("/api/tools", methods=["GET"])
def get_tools():
    tools = Tool.query.all()
    return jsonify([{"id": t.id, "name": t.name} for t in tools])


@app.route("/api/tools", methods=["POST"])
def create_tool():
    data = request.json
    tool = Tool(name=data["name"])
    db.session.add(tool)
    db.session.commit()
    return jsonify({"message": "Tool created successfully"})


@app.route("/api/tools/<int:id>", methods=["DELETE"])
def delete_tool(id):
    tool = Tool.query.get_or_404(id)
    db.session.delete(tool)
    db.session.commit()
    return jsonify({"message": "Tool deleted successfully"})


@app.route("/api/departments", methods=["GET"])
def get_departments():
    departments = Department.query.all()
    return jsonify([{"id": d.id, "name": d.name} for d in departments])


@app.route("/api/departments", methods=["POST"])
def create_department():
    data = request.json
    department = Department(name=data["name"])
    db.session.add(department)
    db.session.commit()
    return jsonify({"message": "Department created successfully"})


@app.route("/api/departments/<int:id>", methods=["DELETE"])
def delete_department(id):
    department = Department.query.get_or_404(id)
    db.session.delete(department)
    db.session.commit()
    return jsonify({"message": "Department deleted successfully"})


@app.route("/api/access-rights", methods=["GET"])
def get_access_rights():
    access_rights = AccessRight.query.all()
    return jsonify([{"id": a.id, "name": a.name} for a in access_rights])


@app.route("/api/access-rights", methods=["POST"])
def create_access_right():
    data = request.json
    access_right = AccessRight(name=data["name"])
    db.session.add(access_right)
    db.session.commit()
    return jsonify({"message": "Access right created successfully"})


@app.route("/api/access-rights/<int:id>", methods=["DELETE"])
def delete_access_right(id):
    access_right = AccessRight.query.get_or_404(id)
    db.session.delete(access_right)
    db.session.commit()
    return jsonify({"message": "Access right deleted successfully"})


@click.command(name="reset-db")
@with_appcontext
def reset_db():
    """Clear the existing data and create new tables."""
    if os.path.exists("instance/operations.db"):
        os.remove("instance/operations.db")
    db.create_all()

    # Add initial data
    default_departments = [
        "Accounting",
        "C-Level",
        "Operations",
        "Sales",
        "Marketing",
        "NOC",
        "Legal",
        "HR",
        "Procurement",
        "IT",
        "Engineering",
        "Economics",
        "Corporate Accounts",
        "Customer Experience",
        "Customer Success",
        "C-Level's team",
    ]

    default_tools = [
        "Mookee",
        "MS Teams",
        "Sharepoint",
        "Office 365",
        "Azure",
        "Hubspot",
        "Mookee Admin",
        "TalentHR",
        "Jira",
        "CyclR",
        "Entersoft",
        "Canva",
        "AppCue",
        "Zoho",
        "Mention",
        "Kibana",
        "Microsip-Elastix",
        "AWS",
        "UChat",
        "ΕΡΓΑΝΗ",
        "Sepenet",
        "Business Μισθοδοσίας",
        "Telq",
        "PC Access",
        "AKS Infrastructure",
        "Devs Azure",
        "Mookee Staging",
        "MySQL Dev",
        "Email Platform",
        "Extranet",
        "AlphaBank (KS)",
        "AlphaBank (Tonia)",
        "Attica Bank",
        "Bankinter (Spain)",
        "BMW Financial Services",
        "Erste Bank (Austria)",
        "Eurobank (Tonia)",
        "NBG (Tonia)",
        "Optima Bank (Tonia)",
        "Paypal",
        "Revolut",
        "Revolut (Spain)",
        "Revolut (Tonia)",
        "Viva Wallet",
        "Τράπεζα Κύπρου",
        "Παγκρήτια Τράπεζα",
        "Πειραιώς (Tonia)",
        "Πειραιώς Statements",
        "Τράπεζα Κύπρου (Napastilo)",
        "Flexfin",
        "Lenderwize (KS)",
        "Lenderwize (Tonia)",
        "42 Platform",
        "Adobe",
        "Aegeanair.com",
        "Airbnb.gr",
        "Amazon",
        "Blueground",
        "Booking.com",
        "Cosmote",
        "Entersoft E-Invoicing",
        "EIT",
        "Entersoft Cloud",
        "<EMAIL>",
        "Eurodns",
        "Expressvpn",
        "FINDBIZ",
        "ixLink (Sparkle)",
        "Killbill",
        "Leaseplan / myFleet",
        "Localphone",
        "Mellon Technologies (Υποστήριξη)",
        "Modulus",
        "Myavis Portal",
        "ΗΡΩΝ",
        "Namecheap.com",
        "Papaki.gr",
        "Paperless",
        "Paperless-connect.gr",
        "Randstad",
        "Rest Station Iomart",
        "Stripe",
        "Taxheaven",
        "TAXIS-APIFON",
        "Twilio",
        "Viber Portal",
        "Vodafone",
        "Wedoconnect",
        "Wizzair",
        "ΑΤΛΑΣ-ΠΡΑΚΤΙΚΗ ΑΕΙ",
        "ΓΕΜΗ",
        "ΕΕΤΤ (KS)",
        "ΕΕΤΤ (Tonia)",
        "Ελληνική Αναπτυξιακή Τράπεζα",
        "ΙΚΑ-APIFON",
        "ΟΑΕΔ",
        "ΟΑΕΕ",
        "ΠΛΗΡΟΦΟΡΙΑΚΟ ΣΥΣΤΗΜΑ ΚΡΑΤΙΚΩΝ ΕΝΙΣΧΥΣΕΩΝ",
        "ΠΣΚΕ EAT",
        "ΠΣΚΕ ΑΝΑΠΤΥΞΙΑΚΟΣ",
        "ΠΣΚΕ Ταμείο Εγγυοδοσίας Καινοτομίας",
        "ΣΕΠΕ",
        "ΤΕΙΡΕΣΙΑΣ",
        "ΥΕΚΑ",
        "ΨΗΦΙΑΚΑ ΕΡΓΑΛΕΙΑ ΜΜΕ VOUCHER ΠΛΑΤΦΟΡΜΑ ΓΙΑ ΕΝΗΜΕΡΟΤΗΤΕΣ",
        "ΨΗΦΙΑΚΑ ΕΡΓΑΛΕΙΑ ΜΜΕ VOUCHER ΠΡΟΜΗΘΕΥΤΗΣ",
        "Ψηφιακός Μετασχηματισμός",
        "TOP UP **********",
        "TOP UP **********",
        "TOP UP **********",
        "Accounting Outlook",
        "Tonia Outlook",
        "Fonts.gr",
        "Jumbo",
        "Kotsovolos.gr",
        "Mediamarkt.gr",
        "Myfonts.com",
        "Pc Access",
        "PLAISIO",
        "RabbitMQ",
        "ΑΒ Βασιλόπουλος",
        "Μαυρουδής Αναλώσιμα",
        "TAXIS-ΚΣ",
        "TAXIS-ΦΡΑΓΚΙΣΚΟΣ",
        "Ryanair",
        "ΙΚΑ-ΚΩΣΤΑΣ",
        "ΛΑΕΚ",
        "VPN",
        "Azure Mookee Staging",
        "Azure Devs",
    ]

    default_access_rights = ["User", "Owner", "Admin"]

    for dept in default_departments:
        db.session.add(Department(name=dept))

    for tool in default_tools:
        db.session.add(Tool(name=tool))

    for right in default_access_rights:
        db.session.add(AccessRight(name=right))

    db.session.commit()
    click.echo("Database has been reset and initialized with default data.")


app.cli.add_command(reset_db)


# Add configuration for file uploads
UPLOAD_FOLDER = "temp"
ALLOWED_EXTENSIONS = {"xlsx", "xls", "csv"}

app.config["UPLOAD_FOLDER"] = UPLOAD_FOLDER

# Create upload folder if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)


def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route("/api/employees/import", methods=["POST"])
def import_employees():
    try:
        if "file" not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files["file"]
        if file.filename == "":
            return jsonify({"error": "No file selected"}), 400

        if not allowed_file(file.filename):
            return jsonify({"error": "Invalid file type"}), 400

        # Save file temporarily
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config["UPLOAD_FOLDER"], filename)
        file.save(filepath)

        print(f"File saved to: {filepath}")  # Debug log

        # Read the file
        try:
            if filename.endswith(".csv"):
                df = pd.read_csv(filepath)
            else:
                # Add error handling for Excel reading
                try:
                    df = pd.read_excel(filepath, engine="openpyxl")
                    print(f"DataFrame columns: {df.columns.tolist()}")  # Debug log
                    print(f"First row: {df.iloc[0].to_dict()}")  # Debug log
                except Exception as excel_error:
                    print(f"Excel reading error: {str(excel_error)}")
                    raise excel_error
        finally:
            # Clean up the temporary file
            if os.path.exists(filepath):
                os.remove(filepath)

        # Validate required columns
        required_columns = [
            "Employee Name",
            "Role",
            "Department",
            "Level",
            "Tools Used",
            "Current Access Rights",
            "Ideal Access Rights",
            "Comments",
        ]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return (
                jsonify(
                    {"error": f"Missing required columns: {', '.join(missing_columns)}"}
                ),
                400,
            )

        # Process the data
        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                print(f"Processing row {index}: {row.to_dict()}")  # Debug log
                with db.session.begin_nested():  # Create savepoint
                    # Create or update employee
                    employee_data = {
                        "name": row["Employee Name"],
                        "role": str(row["Role"]) if pd.notna(row["Role"]) else None,
                        "level": str(row["Level"]) if pd.notna(row["Level"]) else None,
                        "comments": (
                            str(row["Comments"])
                            if pd.notna(row.get("Comments"))
                            else None
                        ),
                    }

                    # Handle department
                    if pd.notna(row["Department"]):
                        dept = Department.query.filter_by(
                            name=str(row["Department"])
                        ).first()
                        if dept:
                            employee_data["department_id"] = dept.id
                        else:
                            print(
                                f"Department not found: {row['Department']}"
                            )  # Debug log

                    # Create employee
                    employee = Employee(**employee_data)
                    db.session.add(employee)
                    db.session.flush()  # Get employee ID

                    # Process tools and access rights
                    if pd.notna(row["Tools Used"]) and pd.notna(
                        row["Current Access Rights"]
                    ):
                        tools = str(row["Tools Used"]).split(";")
                        current_rights = str(row["Current Access Rights"]).split(";")
                        ideal_rights = (
                            str(row["Ideal Access Rights"]).split(";")
                            if pd.notna(row.get("Ideal Access Rights"))
                            else [""] * len(tools)
                        )

                        # Ensure all lists have the same length
                        max_len = max(
                            len(tools), len(current_rights), len(ideal_rights)
                        )
                        tools.extend([""] * (max_len - len(tools)))
                        current_rights.extend([""] * (max_len - len(current_rights)))
                        ideal_rights.extend([""] * (max_len - len(ideal_rights)))

                        for tool_name, current_right, ideal_right in zip(
                            tools, current_rights, ideal_rights
                        ):
                            if tool_name.strip():
                                tool = Tool.query.filter_by(
                                    name=tool_name.strip()
                                ).first()
                                if tool:
                                    access = EmployeeToolAccess(
                                        employee_id=employee.id, tool_id=tool.id
                                    )

                                    if current_right.strip():
                                        current_access = AccessRight.query.filter_by(
                                            name=current_right.strip()
                                        ).first()
                                        if current_access:
                                            access.current_access_id = current_access.id
                                        else:
                                            print(
                                                f"Current access right not found: {current_right}"
                                            )  # Debug log

                                    if ideal_right.strip():
                                        ideal_access = AccessRight.query.filter_by(
                                            name=ideal_right.strip()
                                        ).first()
                                        if ideal_access:
                                            access.ideal_access_id = ideal_access.id
                                        else:
                                            print(
                                                f"Ideal access right not found: {ideal_right}"
                                            )  # Debug log

                                    db.session.add(access)
                                else:
                                    print(f"Tool not found: {tool_name}")  # Debug log

                    success_count += 1

            except Exception as e:
                error_count += 1
                error_msg = f"Error in row {index + 2}: {str(e)}"  # +2 for header and 0-based index
                print(error_msg)  # Debug log
                errors.append(error_msg)
                continue

        db.session.commit()
        return jsonify(
            {
                "message": f"Import completed. {success_count} records imported successfully, {error_count} errors.",
                "errors": errors,
            }
        )

    except Exception as e:
        print(f"Import error: {str(e)}")  # Debug log
        db.session.rollback()
        return jsonify({"error": str(e)}), 500


@app.route("/api/dashboard/stats")
def get_dashboard_stats():
    try:
        # Get total counts
        total_employees = Employee.query.count()
        total_tools = Tool.query.count()
        total_departments = Department.query.count()

        # Get access distribution
        access_distribution = (
            db.session.query(AccessRight.name, db.func.count(EmployeeToolAccess.id))
            .outerjoin(
                EmployeeToolAccess,
                AccessRight.id == EmployeeToolAccess.current_access_id,
            )
            .group_by(AccessRight.name)
            .all()
        )

        # Get department distribution
        department_distribution = (
            db.session.query(Department.name, db.func.count(Employee.id))
            .outerjoin(Employee)
            .group_by(Department.name)
            .all()
        )

        # Get tool usage
        tool_usage = (
            db.session.query(Tool.name, db.func.count(EmployeeToolAccess.id))
            .outerjoin(EmployeeToolAccess)
            .group_by(Tool.name)
            .all()
        )

        # Get access gaps (where current != ideal)
        access_gaps = (
            db.session.query(Tool.name, db.func.count(EmployeeToolAccess.id))
            .join(EmployeeToolAccess)
            .filter(
                EmployeeToolAccess.current_access_id
                != EmployeeToolAccess.ideal_access_id
            )
            .group_by(Tool.name)
            .all()
        )

        return jsonify(
            {
                "overview": {
                    "total_employees": total_employees,
                    "total_tools": total_tools,
                    "total_departments": total_departments,
                },
                "access_distribution": {
                    "labels": [item[0] for item in access_distribution],
                    "data": [item[1] for item in access_distribution],
                },
                "department_distribution": {
                    "labels": [item[0] for item in department_distribution],
                    "data": [item[1] for item in department_distribution],
                },
                "tool_usage": {
                    "labels": [item[0] for item in tool_usage],
                    "data": [item[1] for item in tool_usage],
                },
                "access_gaps": {
                    "labels": [item[0] for item in access_gaps],
                    "data": [item[1] for item in access_gaps],
                },
            }
        )
    except Exception as e:
        print(f"Dashboard error: {str(e)}")
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    with app.app_context():
        db.create_all()

    # Import and initialize ngrok
    from pyngrok import ngrok
    import os

    # Kill any existing ngrok processes (Windows-specific)
    os.system("taskkill /im ngrok.exe /f")

    # Set your authtoken
    ngrok.set_auth_token("*************************************************")

    # Set the port for your Flask app
    port = 5000

    # Open an HTTP tunnel with a name to better manage it
    public_url = ngrok.connect(port, name="flaskapp")
    print(f" * Ngrok tunnel active at: {public_url}")

    # Run the app
    app.run(host="0.0.0.0", port=port, debug=True)
