{% extends 'base.html' %}

{% block title %}
  Dashboard
{% endblock %}

{% block content %}
  <!-- Add breadcrumb -->
  <div class="max-w-7xl mx-auto text-sm breadcrumbs mb-4 p-4">
    <ul>
      <li>
        <a href="{{ url_for('accesses_list') }}">Accesses List</a>
      </li>
      <li>
        <a href="{{ url_for('accesses_manage') }}">Data Management</a>
      </li>
      <li>Dashboard</li>
    </ul>
  </div>

  <div class="p-4 max-w-7xl mx-auto" x-data="dashboard()">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-primary-content">Dashboard</h1>
    </div>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="stat bg-primary text-primary-content rounded-box shadow-lg">
        <div class="stat-title">Total Employees</div>
        <div class="stat-value" x-text="stats.overview.total_employees">0</div>
      </div>
      <div class="stat bg-secondary text-secondary-content rounded-box shadow-lg">
        <div class="stat-title">Total Tools</div>
        <div class="stat-value" x-text="stats.overview.total_tools">0</div>
      </div>
      <div class="stat bg-accent text-accent-content rounded-box shadow-lg">
        <div class="stat-title">Total Departments</div>
        <div class="stat-value" x-text="stats.overview.total_departments">0</div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Access Distribution -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Access Rights Distribution</h2>
          <div class="h-[300px]">
            <canvas id="accessDistributionChart"></canvas>
          </div>
        </div>
      </div>

      <!-- Department Distribution -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Employees by Department</h2>
          <div class="h-[300px]">
            <canvas id="departmentDistributionChart"></canvas>
          </div>
        </div>
      </div>

      <!-- Tool Usage -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Tool Usage</h2>
          <div class="h-[300px]">
            <canvas id="toolUsageChart"></canvas>
          </div>
        </div>
      </div>

      <!-- Access Gaps -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Access Rights Gaps</h2>
          <div class="h-[300px]">
            <canvas id="accessGapsChart"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <script>
    function dashboard() {
      return {
        stats: {
          overview: { total_employees: 0, total_tools: 0, total_departments: 0 },
          access_distribution: { labels: [], data: [] },
          department_distribution: { labels: [], data: [] },
          tool_usage: { labels: [], data: [] },
          access_gaps: { labels: [], data: [] }
        },
        charts: {},
    
        async init() {
          // Wait for DOM to be ready
          await this.$nextTick()
          // Fetch data first
          await this.fetchStats()
          // Initialize charts after data is available
          this.initCharts()
        },
    
        async fetchStats() {
          try {
            const response = await axios.get('/api/dashboard/stats')
            console.log('Fetched stats:', response.data) // Debug log
            this.stats = response.data
          } catch (error) {
            console.error('Error fetching dashboard stats:', error)
          }
        },
    
        initCharts() {
          console.log('Initializing charts...') // Debug log
    
          // Ensure canvas elements exist
          const accessDistributionCanvas = document.getElementById('accessDistributionChart')
          const departmentDistributionCanvas = document.getElementById('departmentDistributionChart')
          const toolUsageCanvas = document.getElementById('toolUsageChart')
          const accessGapsCanvas = document.getElementById('accessGapsChart')
    
          if (!accessDistributionCanvas || !departmentDistributionCanvas || !toolUsageCanvas || !accessGapsCanvas) {
            console.error('One or more canvas elements not found')
            return
          }
    
          // Set chart defaults
          Chart.defaults.color = '#000000' // Set default text color
          Chart.defaults.font.family = "'Inter', sans-serif" // Set default font
    
          // Access Distribution Chart
          this.charts.accessDistribution = new Chart(accessDistributionCanvas, {
            type: 'pie',
            data: {
              labels: this.stats.access_distribution.labels,
              datasets: [
                {
                  data: this.stats.access_distribution.data,
                  backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'bottom',
                  labels: {
                    color: '#000000'
                  }
                }
              }
            }
          })
    
          // Department Distribution Chart
          this.charts.departmentDistribution = new Chart(departmentDistributionCanvas, {
            type: 'bar',
            data: {
              labels: this.stats.department_distribution.labels,
              datasets: [
                {
                  label: 'Employees',
                  data: this.stats.department_distribution.data,
                  backgroundColor: '#36A2EB'
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    color: '#000000'
                  }
                },
                x: {
                  ticks: {
                    color: '#000000'
                  }
                }
              },
              plugins: {
                legend: {
                  labels: {
                    color: '#000000'
                  }
                }
              }
            }
          })
    
          // Tool Usage Chart
          this.charts.toolUsage = new Chart(toolUsageCanvas, {
            type: 'bar',
            data: {
              labels: this.stats.tool_usage.labels,
              datasets: [
                {
                  label: 'Users',
                  data: this.stats.tool_usage.data,
                  backgroundColor: '#FFCE56'
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    color: '#000000'
                  }
                },
                x: {
                  ticks: {
                    color: '#000000'
                  }
                }
              },
              plugins: {
                legend: {
                  labels: {
                    color: '#000000'
                  }
                }
              }
            }
          })
    
          // Access Gaps Chart
          this.charts.accessGaps = new Chart(accessGapsCanvas, {
            type: 'bar',
            data: {
              labels: this.stats.access_gaps.labels,
              datasets: [
                {
                  label: 'Gaps',
                  data: this.stats.access_gaps.data,
                  backgroundColor: '#FF6384'
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    color: '#000000'
                  }
                },
                x: {
                  ticks: {
                    color: '#000000'
                  }
                }
              },
              plugins: {
                legend: {
                  labels: {
                    color: '#000000'
                  }
                }
              }
            }
          })
    
          console.log('Charts initialized') // Debug log
        },
    
        updateCharts() {
          if (!this.charts.accessDistribution) {
            console.log('Charts not initialized yet, initializing...') // Debug log
            this.initCharts()
            return
          }
    
          // Update all charts
          Object.keys(this.charts).forEach((chartKey) => {
            const chart = this.charts[chartKey]
            if (chart && this.stats[chartKey.toLowerCase()]) {
              chart.data.labels = this.stats[chartKey.toLowerCase()].labels
              chart.data.datasets[0].data = this.stats[chartKey.toLowerCase()].data
              chart.update()
            }
          })
        }
      }
    }
  </script>
{% endblock %}
