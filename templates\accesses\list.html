{% extends "base.html" %}

{% block title %}Access Management{% endblock %}

{% block content %}
  <!-- Add breadcrumb -->
  <div class="max-w-7xl mx-auto text-sm breadcrumbs mb-4 p-4">
    <ul>
      <li>
        <a href="{{ url_for('accesses_manage') }}">Data Management</a>
      </li>
      <li>
        <a href="{{ url_for('accesses_dashboard') }}">Dashboard</a>
      </li>
      <li>Accesses List</li>
    </ul>
  </div>
  </div>

  <div class="p-4 max-w-7xl mx-auto" x-data="accessManagement()">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-primary-content">Accesses Management</h1>
        <div class="mt-4 flex justify-between items-center">
            <div class="flex space-x-4">
                <button @click="showAddModal = true" class="btn btn-primary">Add Employee</button>
                
                <!-- Updated Export Dropdown -->
                <div class="dropdown">
                    <button tabindex="0" class="btn btn-secondary">
                        Export
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-lg bg-base-200 rounded-box w-52">
                        <li>
                            <a @click="exportData('csv')" class="text-primary-content hover:bg-white/40 focus:bg-primary focus:text-primary-content">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                CSV
                            </a>
                        </li>
                        <li>
                            <a @click="exportData('excel')" class="text-primary-content hover:bg-white/40 focus:bg-primary focus:text-primary-content">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Excel
                            </a>
                        </li>
                        <li>
                            <a @click="exportData('pdf')" class="text-primary-content hover:bg-white/40 focus:bg-primary focus:text-primary-content">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                                PDF
                            </a>
                        </li>
                    </ul>
                </div>
                
                <button @click="showImportModal = true" class="btn btn-accent">Import</button>
            </div>

            <!-- Search Input -->
            <div class="form-control">
                <div class="flex input-group">
                    <p class="mr-2 mt-3">
                        <svg xmlns="http://www.w3.org/2000/svg" 
                             class="h-6 w-6" 
                             fill="none" 
                             viewBox="0 0 24 24" 
                             stroke="green">
                            <path stroke-linecap="round" 
                                  stroke-linejoin="round" 
                                  stroke-width="2" 
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </p>
                    <input type="text" 
                           placeholder="employees, tools, etc..." 
                           class="input input-bordered w-64"
                           x-model="searchQuery"
                           @input="handleSearch">
                    <!-- Clear button -->
                    <button class="btn btn-square ml-2" 
                            @click="searchQuery = ''; handleSearch()"
                            x-show="searchQuery">
                        <svg xmlns="http://www.w3.org/2000/svg" 
                             class="h-5 w-5" 
                             fill="none" 
                             viewBox="0 0 24 24" 
                             stroke="currentColor">
                            <path stroke-linecap="round" 
                                  stroke-linejoin="round" 
                                  stroke-width="2" 
                                  d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                    
                </div>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
        <table class="table w-full">
            <thead>
                <tr class="text-primary-content font-bold border-b-4 border-primary-content/40">
                    <th class="cursor-pointer w-[15%]" @click="sortBy('name')">
                        Employee Name
                        <span x-show="sortColumn === 'name'">
                            <span x-show="sortDirection === 'asc'">&uarr;</span>
                            <span x-show="sortDirection === 'desc'">&darr;</span>
                        </span>
                    </th>
                    <th class="w-[20%]">Role</th>
                    <th class="cursor-pointer w-[30%]" @click="sortBy('department')">
                        Department
                        <span x-show="sortColumn === 'department'">
                            <span x-show="sortDirection === 'asc'">&uarr;</span>
                            <span x-show="sortDirection === 'desc'">&darr;</span>
                        </span>
                    </th>
                    <th class="w-[10%]">Level</th>
                    <th class="w-[25%]">Tool</th>
                    <th class="w-[5%]">Current Access</th>
                    <th class="w-[5%]">Ideal Access</th>
                    <th class="w-[5%]">Comments</th>
                    <th class="w-[5%]">Actions</th>
                </tr>
            </thead>
            <tbody>
                <template x-for="(employee, index) in paginatedEmployees" :key="`${employee.id}-${index}`">
                    <tr class="border-b border-primary-content/40">
                        <td x-text="employee.name || '-'"></td>
                        <td>
                            <span x-text="employee.role || '-'"
                                  :title="employee.role"
                                  class="inline-block truncate max-w-[150px]">
                            </span>
                        </td>
                        <td x-text="employee.department || '-'"></td>
                        <td x-text="employee.level || '-'"></td>
                        <td>
                            <span x-text="employee.tool?.name || '-'" 
                                  :title="employee.tool?.name"
                                  :class="employee.tool ? 'badge truncate max-w-[150px]' : ''"
                                  :style="employee.tool ? { backgroundColor: getToolColor(employee.tool.name) } : {}"
                                  class="text-white inline-block">
                            </span>
                        </td>
                        <td>
                            <span x-text="employee.current_access?.name || '-'"
                                  :class="employee.current_access ? 'badge' : ''"
                                  :style="employee.current_access ? { backgroundColor: getAccessColor(employee.current_access.name) } : {}"
                                  class="text-white">
                            </span>
                        </td>
                        <td>
                            <span x-text="employee.ideal_access?.name || '-'"
                                  :class="employee.ideal_access ? 'badge badge-outline' : ''"
                                  :style="employee.ideal_access ? { 
                                      borderColor: getAccessColor(employee.ideal_access.name),
                                      color: getAccessColor(employee.ideal_access.name)
                                  } : {}"
                                  >
                            </span>
                        </td>
                        <td x-text="employee.comments || '-'"></td>
                        <td>
                            <div class="flex space-x-2">
                                <!-- Show actions only on first occurrence of employee -->
                                <template x-if="!paginatedEmployees.slice(0, index).some(e => e.id === employee.id)">
                                    <div class="flex space-x-2">
                                        <button @click="editEmployee(employee)" class="btn btn-sm">Edit</button>
                                        <button @click="deleteEmployee(employee.id)" class="btn btn-sm btn-error text-white">Delete</button>
                                    </div>
                                </template>
                            </div>
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>

        <!-- Pagination -->
        <div class="flex justify-between items-center mt-4">
            <div class="text-sm text-primary-content">
                Showing <span x-text="startIndex + 1"></span> to 
                <span x-text="Math.min(endIndex, filteredEmployees.length)"></span> of 
                <span x-text="filteredEmployees.length"></span> entries
            </div>
            <div class="join">
                <!-- Previous button -->
                <button class="join-item btn" 
                        :class="currentPage === 1 ? 'btn-disabled' : ''"
                        @click="currentPage--" 
                        :disabled="currentPage === 1">
                    Previous
                </button>
                
                <!-- First page -->
                <button class="join-item btn" 
                        :class="currentPage === 1 ? 'btn-active' : ''"
                        @click="currentPage = 1"
                        x-show="totalPages > 0">
                    1
                </button>

                <!-- Left ellipsis -->
                <button class="join-item btn btn-disabled" 
                        x-show="startPage > 2">
                    ...
                </button>

                <!-- Page numbers -->
                <template x-for="page in visiblePages" :key="page">
                    <button class="join-item btn" 
                            :class="currentPage === page ? 'btn-active' : ''"
                            @click="currentPage = page"
                            x-text="page">
                    </button>
                </template>

                <!-- Right ellipsis -->
                <button class="join-item btn btn-disabled" 
                        x-show="endPage < totalPages - 1">
                    ...
                </button>

                <!-- Last page -->
                <button class="join-item btn" 
                        :class="currentPage === totalPages ? 'btn-active' : ''"
                        @click="currentPage = totalPages"
                        x-show="totalPages > 1"
                        x-text="totalPages">
                </button>

                <!-- Next button -->
                <button class="join-item btn" 
                        :class="currentPage === totalPages ? 'btn-disabled' : ''"
                        @click="currentPage++" 
                        :disabled="currentPage === totalPages">
                    Next
                </button>
            </div>
            <div class="form-control">
                <select class="select select-bordered" x-model="pageSize">
                    <option value="10">10 per page</option>
                    <option value="25">25 per page</option>
                    <option value="50">50 per page</option>
                    <option value="100">100 per page</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal" :class="{ 'modal-open': showAddModal || showEditModal }">
        <div class="modal-box max-w-4xl">
            <h3 class="font-bold text-lg mb-4" x-text="modalTitle"></h3>
            <form @submit.prevent="saveEmployee">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Employee Name</span>
                            <span class="label-text-alt text-error">*</span>
                        </label>
                        <input type="text" x-model="formData.name" class="input input-bordered" required>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Role</span>
                        </label>
                        <input type="text" x-model="formData.role" class="input input-bordered">
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Department</span>
                        </label>
                        <select x-model="formData.department_id" class="select select-bordered">
                            <option value="">Select Department</option>
                            <template x-for="dept in departments" :key="dept.id">
                                <option :value="dept.id" x-text="dept.name"></option>
                            </template>
                        </select>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Level</span>
                        </label>
                        <input type="text" x-model="formData.level" class="input input-bordered">
                    </div>
                </div>

                <!-- Tool Access Section -->
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="font-semibold text-primary-content">Tool Access</h4>
                        <button type="button" @click="addToolAccess" class="btn btn-sm btn-primary">
                            Add Tool
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <template x-for="(access, index) in formData.tool_access" :key="index">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-base-300 rounded-lg">
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text text-white">Tool</span>
                                    </label>
                                    <select x-model="access.tool_id" 
                                            class="select select-bordered bg-base-200 text-primary-content">
                                        <option value="">Select Tool</option>
                                        <template x-for="tool in tools" :key="tool.id">
                                            <option :value="tool.id.toString()" 
                                                    :selected="tool.id.toString() === access.tool_id"
                                                    x-text="tool.name">
                                            </option>
                                        </template>
                                    </select>
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text text-white">Current Access</span>
                                    </label>
                                    <select x-model="access.current_access_id" 
                                            class="select select-bordered bg-base-200 text-primary-content">
                                        <option value="">Select Access Level</option>
                                        <template x-for="right in accessRights" :key="right.id">
                                            <option :value="right.id.toString()"
                                                    :selected="right.id.toString() === access.current_access_id"
                                                    x-text="right.name">
                                            </option>
                                        </template>
                                    </select>
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text text-white">Ideal Access</span>
                                    </label>
                                    <select x-model="access.ideal_access_id" 
                                            class="select select-bordered bg-base-200 text-primary-content">
                                        <option value="">Select Access Level</option>
                                        <template x-for="right in accessRights" :key="right.id">
                                            <option :value="right.id.toString()"
                                                    :selected="right.id.toString() === access.ideal_access_id"
                                                    x-text="right.name">
                                            </option>
                                        </template>
                                    </select>
                                </div>

                                <button type="button" 
                                        @click="removeToolAccess(index)" 
                                        class="btn btn-error btn-sm md:col-span-3 text-white">
                                    Remove Tool
                                </button>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Comments -->
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Comments</span>
                    </label>
                    <textarea x-model="formData.comments" class="textarea textarea-bordered h-24"></textarea>
                </div>

                <div class="modal-action">
                    <button type="submit" class="btn btn-primary">Save</button>
                    <button type="button" @click="closeModal" class="btn">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Import Modal -->
    <div class="modal" :class="{ 'modal-open': showImportModal }">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Import Data</h3>
            <div class="my-4">
                <div class="alert alert-info mb-4">
                    <div>
                        <h4 class="font-bold">Required Format:</h4>
                        <p class="text-sm">Columns: Employee Name,Role,Department,Level,Tool,Current Access,Ideal Access,Comments</p>
                        <p class="text-sm mt-2">Example: John Doe,Engineer,IT,Senior,MS Teams;Sharepoint,User;Owner,Owner;Owner,Some comments</p>
                    </div>
                </div>
                <input type="file" class="file-input file-input-bordered w-full" 
                       @change="handleFileUpload" 
                       accept=".xlsx,.xls,.csv">
            </div>
            <div class="modal-action">
                <button @click="importData" 
                        class="btn btn-primary" 
                        :disabled="!selectedFile">Import</button>
                <button @click="closeImportModal" class="btn">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
function accessManagement() {
    return {
        employees: [],
        departments: [],
        tools: [],
        accessRights: [],
        showAddModal: false,
        showEditModal: false,
        showImportModal: false,
        modalTitle: 'Add Employee',
        formData: {
            id: null,
            name: '',
            role: '',
            department_id: '',
            level: '',
            comments: '',
            tool_access: []
        },
        selectedFile: null,
        searchQuery: '',
        currentPage: 1,
        pageSize: 10,
        sortColumn: 'name',
        sortDirection: 'asc',
        toolColors: {},     // Cache for tool colors
        accessColors: {},   // Cache for access colors

        get filteredEmployees() {
            let employees = [...this.employees];
            
            // Apply search filter
            if (this.searchQuery) {
                const query = this.searchQuery.toLowerCase();
                employees = employees.filter(emp => 
                    emp.name?.toLowerCase().includes(query) ||
                    emp.role?.toLowerCase().includes(query) ||
                    emp.department?.toLowerCase().includes(query) ||
                    emp.level?.toLowerCase().includes(query) ||
                    emp.tool?.name?.toLowerCase().includes(query) ||
                    emp.current_access?.name?.toLowerCase().includes(query) ||
                    emp.ideal_access?.name?.toLowerCase().includes(query) ||
                    emp.comments?.toLowerCase().includes(query)
                );
            }

            // Apply sorting
            employees.sort((a, b) => {
                let aValue, bValue;
                
                switch(this.sortColumn) {
                    case 'name':
                        aValue = a.name?.toLowerCase() ?? '';
                        bValue = b.name?.toLowerCase() ?? '';
                        break;
                    case 'department':
                        aValue = a.department?.toLowerCase() ?? '';
                        bValue = b.department?.toLowerCase() ?? '';
                        break;
                    default:
                        return 0;
                }

                if (this.sortDirection === 'asc') {
                    return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                } else {
                    return bValue < aValue ? -1 : bValue > aValue ? 1 : 0;
                }
            });

            return employees;
        },

        get totalPages() {
            return Math.ceil(this.filteredEmployees.length / this.pageSize);
        },

        get pages() {
            const pages = [];
            for (let i = 1; i <= this.totalPages; i++) {
                pages.push(i);
            }
            return pages;
        },

        get startIndex() {
            return (this.currentPage - 1) * this.pageSize;
        },

        get endIndex() {
            return this.startIndex + parseInt(this.pageSize);
        },

        get paginatedEmployees() {
            return this.filteredEmployees.slice(this.startIndex, this.endIndex);
        },

        handleSearch() {
            this.currentPage = 1; // Reset to first page when searching
        },

        init() {
            this.$watch('pageSize', () => {
                this.currentPage = 1; // Reset to first page when changing page size
            });

            this.$nextTick(() => {
                this.fetchEmployees()
                this.fetchDepartments()
                this.fetchTools()
                this.fetchAccessRights()
            })
        },

        async fetchEmployees() {
            try {
                const response = await axios.get('/api/employees')
                if (response.data && Array.isArray(response.data)) {
                    // Ensure all properties exist to prevent undefined errors
                    this.employees = response.data.map(emp => ({
                        id: emp.id,
                        name: emp.name || '',
                        role: emp.role || '',
                        department: emp.department || '',
                        department_id: emp.department_id || '',
                        level: emp.level || '',
                        comments: emp.comments || '',
                        tool: emp.tool || null,
                        current_access: emp.current_access || null,
                        ideal_access: emp.ideal_access || null
                    }))
                    console.log('Fetched employees:', this.employees)
                }
            } catch (error) {
                console.error('Error fetching employees:', error)
            }
        },

        async fetchDepartments() {
            try {
                const response = await axios.get('/api/departments')
                this.departments = response.data
            } catch (error) {
                console.error('Error fetching departments:', error)
            }
        },

        async fetchTools() {
            try {
                const response = await axios.get('/api/tools')
                this.tools = response.data
            } catch (error) {
                console.error('Error fetching tools:', error)
            }
        },

        async fetchAccessRights() {
            try {
                const response = await axios.get('/api/access-rights')
                this.accessRights = response.data
            } catch (error) {
                console.error('Error fetching access rights:', error)
            }
        },

        editEmployee(employee) {
            console.log('Original employee data:', employee); // Debug log
            this.modalTitle = 'Edit Employee';
            
            // Get all rows for this employee
            const employeeRows = this.employees.filter(e => e.id === employee.id);
            
            // Construct tool_access array from all rows
            const tool_access = employeeRows
                .filter(row => row.tool) // Filter out rows without tools
                .map(row => ({
                    tool_id: row.tool.id.toString(),
                    current_access_id: row.current_access?.id.toString() || '',
                    ideal_access_id: row.ideal_access?.id.toString() || ''
                }));

            this.formData = {
                id: employee.id,
                name: employee.name,
                role: employee.role || '',
                department_id: employee.department_id ? employee.department_id.toString() : '',
                level: employee.level || '',
                comments: employee.comments || '',
                tool_access: tool_access.length > 0 ? tool_access : []
            };
            
            console.log('Processed formData:', this.formData); // Debug log
            this.showEditModal = true;
        },

        getAccessRightId(accessName) {
            if (!accessName) return '';
            const accessRight = this.accessRights.find(right => right.name === accessName);
            return accessRight ? accessRight.id : '';
        },

        async deleteEmployee(id) {
            if (confirm('Are you sure you want to delete this employee?')) {
                try {
                    await axios.delete(`/api/employees/${id}`)
                    this.fetchEmployees()
                } catch (error) {
                    console.error('Error deleting employee:', error)
                }
            }
        },

        addToolAccess() {
            this.formData.tool_access.push({
                tool_id: '',
                current_access_id: '',
                ideal_access_id: ''
            })
        },

        removeToolAccess(index) {
            this.formData.tool_access.splice(index, 1)
        },

        resetForm() {
            this.formData = {
                id: null,
                name: '',
                role: '',
                department_id: '',
                level: '',
                comments: '',
                tool_access: []
            }
        },

        async saveEmployee() {
            try {
                let response;
                if (this.showEditModal) {
                    response = await axios.put(`/api/employees/${this.formData.id}`, this.formData)
                } else {
                    response = await axios.post('/api/employees', this.formData)
                }

                if (response.data) {
                    // Wait for the database operation to complete
                    await new Promise(resolve => setTimeout(resolve, 100));
                    await this.fetchEmployees()
                    this.closeModal()
                }
            } catch (error) {
                console.error('Error saving employee:', error)
                alert('Error saving employee. Please try again.')
            }
        },

        closeModal() {
            this.showAddModal = false
            this.showEditModal = false
            this.resetForm()
        },

        exportData(format) {
            window.location.href = `/api/employees/export/${format}`
        },

        handleFileUpload(event) {
            this.selectedFile = event.target.files[0];
        },

        async importData() {
            if (!this.selectedFile) {
                alert('Please select a file to import');
                return;
            }

            const formData = new FormData();
            formData.append('file', this.selectedFile);

            try {
                const response = await axios.post('/api/employees/import', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });

                if (response.data.errors && response.data.errors.length > 0) {
                    alert(`Import completed with errors:\n${response.data.errors.join('\n')}`);
                } else {
                    alert(response.data.message);
                }

                await this.fetchEmployees();
                this.closeImportModal();
            } catch (error) {
                console.error('Import error:', error);
                alert('Error importing data. Please check the file format and try again.');
            }
        },

        closeImportModal() {
            this.showImportModal = false;
            this.selectedFile = null;
        },

        sortBy(column) {
            if (this.sortColumn === column) {
                // If clicking the same column, toggle direction
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                // If clicking a new column, set it with ascending direction
                this.sortColumn = column;
                this.sortDirection = 'asc';
            }
        },

        get startPage() {
            if (this.totalPages <= 7) return 2;
            const start = this.currentPage - 2;
            return Math.max(2, Math.min(start, this.totalPages - 4));
        },

        get endPage() {
            if (this.totalPages <= 7) return this.totalPages - 1;
            const end = this.currentPage + 2;
            return Math.min(this.totalPages - 1, Math.max(end, 6));
        },

        get visiblePages() {
            const pages = [];
            for (let i = this.startPage; i <= this.endPage; i++) {
                pages.push(i);
            }
            return pages;
        },

        stringToColor(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                hash = str.charCodeAt(i) + ((hash << 5) - hash);
            }
            
            const h = Math.abs(hash % 360);
            const s = 65;
            const l = 45;
            
            return `hsl(${h}, ${s}%, ${l}%)`;
        },

        getToolColor(toolName) {
            if (!this.toolColors[toolName]) {
                // Predefined colors for tools
                const toolColorMap = {
                    'MS Teams': '#6B21A8',     // purple-800
                    'Sharepoint': '#0E7490',   // cyan-700
                    'Office 365': '#0369A1',   // sky-700
                    'Azure': '#1D4ED8',        // blue-700
                    'Hubspot': '#BE185D',      // pink-700
                    'Mookee Admin': '#B45309', // amber-700
                    'TalentHR': '#15803D',     // green-700
                    'Jira': '#0891B2',         // cyan-600
                    'Entersoft': '#7C2D12',    // orange-900
                    'Canva': '#4338CA',        // indigo-700
                    'AppCue': '#9333EA',       // purple-600
                    'Zoho': '#A21CAF',         // fuchsia-700
                };

                this.toolColors[toolName] = toolColorMap[toolName] || this.stringToColor(toolName);
            }
            return this.toolColors[toolName];
        },

        getAccessColor(accessName) {
            if (!this.accessColors[accessName]) {
                // Specific colors for access rights
                const accessColorMap = {
                    'User': '#16A34A',    // green-600
                    'Owner': '#2563EB',    // blue-600
                    'Admin': '#F97316',    // orange-500
                };

                this.accessColors[accessName] = accessColorMap[accessName] || this.stringToColor(accessName);
            }
            return this.accessColors[accessName];
        }
    }
}
</script>
{% endblock %}
