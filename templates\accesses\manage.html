{% extends "base.html" %}

{% block title %}Data Management{% endblock %}

{% block content %}
  <!-- Add breadcrumb -->
  <div class="max-w-7xl mx-auto text-sm breadcrumbs mb-4 p-4">
    <ul>
      <li>
        <a href="{{ url_for('accesses_list') }}">Accesses List</a>
      </li>
      <li>
        <a href="{{ url_for('accesses_dashboard') }}">Dashboard</a>
      </li>
      <li>Data Management</li>
    </ul>
  </div>

  <div class="p-4 max-w-7xl mx-auto" x-data="dataManagement()">
    <h1 class="text-2xl font-bold text-primary-content">Data Management</h1>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Tools Management -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">Tools Management</h2>
                <div class="h-[400px] flex flex-col">
                    <div class="overflow-y-auto flex-grow mb-4">
                        <table class="table w-full">
                            <thead class="sticky top-0 bg-base-100">
                                <tr>
                                    <th>Name</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template x-for="tool in tools" :key="tool.id">
                                    <tr>
                                        <td x-text="tool.name"></td>
                                        <td>
                                            <button @click="deleteTool(tool.id)" class="btn btn-xs btn-error text-white">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <div class="form-control mt-auto">
                        <div class="flex gap-2 input-group">
                            <input type="text" x-model="newTool" placeholder="New tool name" class="input input-bordered w-full">
                            <button @click="addTool" class="btn btn-square bg-green-500 text-white text-2xl hover:bg-green-600">+</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Departments Management -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">Departments Management</h2>
                <div class="h-[400px] flex flex-col">
                    <div class="overflow-y-auto flex-grow mb-4">
                        <table class="table w-full">
                            <thead class="sticky top-0 bg-base-100">
                                <tr>
                                    <th>Name</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template x-for="dept in departments" :key="dept.id">
                                    <tr>
                                        <td x-text="dept.name"></td>
                                        <td>
                                            <button @click="deleteDepartment(dept.id)" class="btn btn-xs btn-error text-white">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <div class="form-control mt-auto">
                        <div class="flex gap-2 input-group">
                            <input type="text" x-model="newDepartment" placeholder="New department name" class="input input-bordered w-full">
                            <button @click="addDepartment" class="btn btn-square bg-green-500 text-white text-2xl hover:bg-green-600">+</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Access Rights Management -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">Access Rights Management</h2>
                <div class="h-[400px] flex flex-col">
                    <div class="overflow-y-auto flex-grow mb-4">
                        <table class="table w-full">
                            <thead class="sticky top-0 bg-base-100">
                                <tr>
                                    <th>Name</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template x-for="access in accessRights" :key="access.id">
                                    <tr>
                                        <td x-text="access.name"></td>
                                        <td>
                                            <button @click="deleteAccessRight(access.id)" class="btn btn-xs btn-error text-white">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <div class="form-control mt-auto">
                        <div class="flex gap-2 input-group">
                            <input type="text" x-model="newAccessRight" placeholder="New access right name" class="input input-bordered w-full">
                            <button @click="addAccessRight" class="btn btn-square bg-green-500 text-white text-2xl hover:bg-green-600">+</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>

<script>
function dataManagement() {
    return {
        tools: [],
        departments: [],
        accessRights: [],
        newTool: '',
        newDepartment: '',
        newAccessRight: '',

        init() {
            this.fetchTools()
            this.fetchDepartments()
            this.fetchAccessRights()
        },

        async fetchTools() {
            try {
                const response = await axios.get('/api/tools')
                this.tools = response.data
            } catch (error) {
                console.error('Error fetching tools:', error)
            }
        },

        async fetchDepartments() {
            try {
                const response = await axios.get('/api/departments')
                this.departments = response.data
            } catch (error) {
                console.error('Error fetching departments:', error)
            }
        },

        async fetchAccessRights() {
            try {
                const response = await axios.get('/api/access-rights')
                this.accessRights = response.data
            } catch (error) {
                console.error('Error fetching access rights:', error)
            }
        },

        async addTool() {
            if (!this.newTool.trim()) return
            try {
                await axios.post('/api/tools', { name: this.newTool })
                this.newTool = ''
                this.fetchTools()
            } catch (error) {
                console.error('Error adding tool:', error)
            }
        },

        async addDepartment() {
            if (!this.newDepartment.trim()) return
            try {
                await axios.post('/api/departments', { name: this.newDepartment })
                this.newDepartment = ''
                this.fetchDepartments()
            } catch (error) {
                console.error('Error adding department:', error)
            }
        },

        async addAccessRight() {
            if (!this.newAccessRight.trim()) return
            try {
                await axios.post('/api/access-rights', { name: this.newAccessRight })
                this.newAccessRight = ''
                this.fetchAccessRights()
            } catch (error) {
                console.error('Error adding access right:', error)
            }
        },

        async deleteTool(id) {
            if (confirm('Are you sure you want to delete this tool?')) {
                try {
                    await axios.delete(`/api/tools/${id}`)
                    this.fetchTools()
                } catch (error) {
                    console.error('Error deleting tool:', error)
                }
            }
        },

        async deleteDepartment(id) {
            if (confirm('Are you sure you want to delete this department?')) {
                try {
                    await axios.delete(`/api/departments/${id}`)
                    this.fetchDepartments()
                } catch (error) {
                    console.error('Error deleting department:', error)
                }
            }
        },

        async deleteAccessRight(id) {
            if (confirm('Are you sure you want to delete this access right?')) {
                try {
                    await axios.delete(`/api/access-rights/${id}`)
                    this.fetchAccessRights()
                } catch (error) {
                    console.error('Error deleting access right:', error)
                }
            }
        }
    }
}
</script>
{% endblock %}
