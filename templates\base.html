<!DOCTYPE html>
<html lang="en" data-theme="cyberpunk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Operations - {% block title %}{% endblock %}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS and DaisyUI CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.7.2/dist/full.min.css" rel="stylesheet" type="text/css" />
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Add custom CSS after DaisyUI to override background colors -->
    <style>
        /* Override background colors while keeping other cyberpunk styling */
        :root {
            --fallback-b1: #F1E3D3;  /* Base color - warm beige */
            --fallback-b2: #E5D7C7;  /* Slightly darker (about 5% darker) */
            --fallback-b3:rgb(43, 43, 42);  /* Even darker (about 10% darker) */
        }

        body {
            background-color: var(--fallback-b1) !important;
            font-family: 'Inter', sans-serif;

        }

        .bg-base-100 {
            background-color: var(--fallback-b1) !important;
        }

        .bg-base-200 {
            background-color: var(--fallback-b2) !important;
        }

        .bg-base-300 {
            background-color: var(--fallback-b3) !important;
        }

        /* Keep modal background dark */
        .modal-box {
            background-color: var(--fallback-b2) !important;
        }

        /* Keep table styling consistent */
        .table {
            background-color: var(--fallback-b2) !important;
        }

        /* Keep dropdown menus dark */
        .dropdown-content {
            background-color: var(--fallback-b2) !important;
        }

        /* Keep navbar dark */
        nav {
            background-color: var(--fallback-b3) !important;
        }

        /* Keep footer dark */
        footer {
            background-color: var(--fallback-b3) !important;
        }
    </style>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {}
            },
            plugins: [require("daisyui")],
            daisyui: {
                themes: ["cyberpunk"],
            },
        }
    </script>
    
</head>
<body class="flex flex-col min-h-screen">
    <!-- Navbar -->
    <nav class="bg-black shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between py-3">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="/" class="text-white text-2xl font-semibold">Operations</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <!-- Accesses Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" 
                                    @click.away="open = false"
                                    class="text-white hover:text-white/80 px-3 py-2 rounded-md font-medium inline-flex items-center">
                                <span>Accesses</span>
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div x-show="open"
                                 x-cloak
                                 class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-black ring-1 ring-white ring-opacity-5">
                                <div class="py-1 dropdown-content">
                                    <a href="/accesses" class="block px-4 py-2 text-sm text-primary-content hover:bg-white/40">Accesses List</a>
                                    <a href="/accesses/manage" class="block px-4 py-2 text-sm text-primary-content hover:bg-white/40">Data Management</a>
                                    <a href="/accesses/dashboard" class="block px-4 py-2 text-sm text-primary-content hover:bg-white/40">Dashboard</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Mobile menu button -->
                <div class="flex items-center sm:hidden" x-data="{ mobileMenuOpen: false }">
                    <button type="button" 
                            @click="mobileMenuOpen = !mobileMenuOpen" 
                            class="inline-flex items-center justify-center p-2 rounded-md text-white hover:text-white/80">
                        <svg class="h-6 w-6" x-show="!mobileMenuOpen" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                        <svg class="h-6 w-6" x-show="mobileMenuOpen" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-cloak>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                    <!-- Mobile menu -->
                    <div class="dropdown-content absolute top-16 left-0 w-full sm:hidden" 
                         x-show="mobileMenuOpen"
                         x-cloak>
                        <div class="px-2 pt-2 pb-3 space-y-1">
                            <!-- Mobile Accesses Menu -->
                            <div x-data="{ subMenuOpen: false }">
                                <button @click="subMenuOpen = !subMenuOpen"
                                        class="text-primary-content w-full text-left px-3 py-2 rounded-md text-base font-medium inline-flex items-center justify-between hover:bg-white/40">
                                    <span>Accesses</span>
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>
                                <div x-show="subMenuOpen" x-cloak class="pl-4 pb-2">
                                    <a href="/accesses" class="text-primary-content block px-3 py-2 rounded-md font-medium hover:bg-white/40">Access List</a>
                                    <a href="/accesses/manage" class="text-primary-content block px-3 py-2 rounded-md font-medium hover:bg-white/40">Data Management</a>
                                    <a href="/accesses/dashboard" class="text-primary-content block px-3 py-2 rounded-md font-medium hover:bg-white/40">Dashboard</a>
                                </div>
                            </div>
                            <a href="/contact" class="text-primary-content block px-3 py-2 rounded-md font-medium hover:bg-white/40">Tools</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-grow container mx-auto px-4 py-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-black text-white">
        <div class="max-w-7xl mx-auto px-4 py-6">
            <div class="text-center">
                <p>&copy; 2025 K.D. Apifon Operations Dept.</p>
            </div>
        </div>
    </footer>
</body>

</html>
