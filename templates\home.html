{% extends 'base.html' %}

{% block title %}
  Home
{% endblock %}

{% block content %}
  <div class="flex flex-col justify-center items-center min-h-[60vh] space-y-8">
    <div class="text-center space-y-4">
      <h1 class="text-4xl font-bold text-primary-content">Welcome back, Operations Team! 👋</h1>
      <p class="font-['Inter'] text-xl text-primary-content/80">Select an app to get started!</p>
    </div>

    <!-- App Card -->
    <div class="flex flex-row justify-center items-center space-x-4">
      <a href="{{ url_for('accesses_list') }}" class="card w-96 bg-primary shadow-xl hover:scale-105 transition-transform duration-200 cursor-pointer">
        <div class="card-body text-center text-primary-content">
          <div class="mb-4">
            <!-- Key Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
            </svg>
          </div>
          <h2 class="card-title text-2xl justify-center mb-2">Access Management</h2>
          <p class="font-['Inter'] opacity-90">Manage employee access rights across all tools and platforms</p>
        </div>
      </a>
      <!--  <a href="https://ops-tools.streamlit.app/" target="_blank" class="card w-96 bg-secondary shadow-xl hover:scale-105 transition-transform duration-200 cursor-pointer">
        <div class="card-body text-center text-primary-content">
          <div class="mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h2 class="card-title text-2xl justify-center mb-2">File Operations</h2>
          <p class="font-['Inter'] opacity-90">Perform various file operations (PDF, Excel, Images etc.)</p>
        </div>
      </a>  -->
    </div>
  </div>
{% endblock %}
