Metadata-Version: 2.2
Name: pyngrok
Version: 7.2.3
Summary: A Python wrapper for ngrok.
Maintainer-email: <PERSON> <<EMAIL>>
License: MIT License
        
        Copyright (c) 2018-2024 Alex Laird
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
        
Project-URL: Changelog, https://github.com/alexdlaird/pyngrok/blob/main/CHANGELOG.md
Project-URL: Documentation, https://pyngrok.readthedocs.io
Project-URL: Sponsor, https://github.com/sponsors/alexdlaird
Project-URL: Source Code, https://github.com/alexdlaird/pyngrok
Classifier: Development Status :: 6 - Mature
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: OS Independent
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: BSD :: FreeBSD
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Database :: Database Engines/Servers
Classifier: Topic :: Internet :: Proxy Servers
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Internet :: WWW/HTTP :: HTTP Servers
Classifier: Topic :: Software Development :: Build Tools
Classifier: Topic :: Software Development :: Testing
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: PyYAML>=5.1
Provides-Extra: dev
Requires-Dist: pytest; extra == "dev"
Requires-Dist: coverage[toml]; extra == "dev"
Requires-Dist: psutil; extra == "dev"
Requires-Dist: flake8; extra == "dev"
Requires-Dist: flake8-pyproject; extra == "dev"
Requires-Dist: pep8-naming; extra == "dev"
Provides-Extra: docs
Requires-Dist: Sphinx; extra == "docs"
Requires-Dist: sphinx-notfound-page; extra == "docs"
Requires-Dist: sphinx_autodoc_typehints==1.25.2; extra == "docs"
Requires-Dist: sphinx-substitution-extensions; extra == "docs"
Requires-Dist: mypy; extra == "docs"
Requires-Dist: types-PyYAML; extra == "docs"

<p align="center"><img alt="pyngrok - a Python wrapper for ngrok" src="https://pyngrok.readthedocs.io/en/latest/_images/logo.png" /></p>

[![Version](https://img.shields.io/pypi/v/pyngrok)](https://pypi.org/project/pyngrok)
[![Python Versions](https://img.shields.io/pypi/pyversions/pyngrok.svg)](https://pypi.org/project/pyngrok)
[![Coverage](https://img.shields.io/codecov/c/github/alexdlaird/pyngrok)](https://codecov.io/gh/alexdlaird/pyngrok)
[![Build](https://img.shields.io/github/actions/workflow/status/alexdlaird/pyngrok/build.yml)](https://github.com/alexdlaird/pyngrok/actions/workflows/build.yml)
[![Code Quality](https://app.codacy.com/project/badge/Grade/b055cf6e3f1745098fab86a2923730b3)](https://app.codacy.com/gh/alexdlaird/pyngrok/dashboard?utm_source=gh&utm_medium=referral&utm_content=&utm_campaign=Badge_grade)
[![Docs](https://img.shields.io/readthedocs/pyngrok)](https://pyngrok.readthedocs.io/en/latest)
[![GitHub License](https://img.shields.io/github/license/alexdlaird/pyngrok)](https://github.com/alexdlaird/pyngrok/blob/main/LICENSE)

`pyngrok` is a Python wrapper for `ngrok` that manages its own binary, making `ngrok` available via a convenient Python
API.

[`ngrok`](https://ngrok.com) is a reverse proxy tool that opens secure tunnels from public URLs to localhost, perfect
for exposing local web servers, building webhook integrations, enabling SSH access, testing chatbots, demoing from
your own machine, and more, and its made even more powerful with native Python integration through `pyngrok`.

## Installation

`pyngrok` is available on [PyPI](https://pypi.org/project/pyngrok/) and can be installed
using `pip`:

```sh
pip install pyngrok
```

or `conda`:

```sh
conda install -c conda-forge pyngrok
```

That's it! `pyngrok` is now available as a package to our Python projects, and `ngrok` is now available from
the command line.

## Basic Usage

### Open a Tunnel

To open a tunnel, use the [`connect`](https://pyngrok.readthedocs.io/en/latest/api.html#pyngrok.ngrok.connect) method,
which returns a `NgrokTunnel`, and this returned object has a reference to the public URL generated by `ngrok` in its
`public_url` attribute.

```python
from pyngrok import ngrok

# Open a HTTP tunnel on the default port 80
# <NgrokTunnel: "https://<public_sub>.ngrok.io" -> "http://localhost:80">
http_tunnel = ngrok.connect(3)

# Open a SSH tunnel
# <NgrokTunnel: "tcp://0.tcp.ngrok.io:12345" -> "localhost:22">
ssh_tunnel = ngrok.connect("22", "tcp")

# Open a named tunnel from the config file
named_tunnel = ngrok.connect(name="my-config-file-tunnel")
```

The [`connect`](https://pyngrok.readthedocs.io/en/latest/api.html#pyngrok.ngrok.connect) method takes `kwargs` as well, which allows
us to pass additional tunnel configurations that are supported by `ngrok` (or the `name` of a tunnel defined in
`ngrok`'s config file), [as documented here](https://pyngrok.readthedocs.io/en/latest/#tunnel-configurations).

### `ngrok`'s Edges

To use [`ngrok`'s Edges](https://ngrok.com/docs/network-edge/edges/) with `pyngrok`, first [configure an Edge on
`ngrok`'s dashboard](https://dashboard.ngrok.com/edges) (with at least one Endpoint mapped to the Edge), and define a
labeled tunnel in [the `ngrok` config file](https://ngrok.com/docs/agent/config/v2/#define-two-labeled-tunnels)
that points to the Edge.

```yaml
tunnels:
  some-edge-tunnel:
    labels:
      - edge=my_edge_id
    addr: http://localhost:80
```

To start a labeled tunnel in `pyngrok`, pass its `name`
to [connect](https://pyngrok.readthedocs.io/en/develop/api.html#pyngrok.ngrok.connect).

```python
from pyngrok import ngrok

# Open the Edge tunnel that is defind in the config file
named_tunnel = ngrok.connect(name="some-edge-tunnel")
```

Once an Edge tunnel is started, it can be managed through [`ngrok`'s dashboard](https://dashboard.ngrok.com/edges).

### Command Line Usage

This package puts the default `ngrok` binary on our path, so all features of `ngrok` are
available on the command line.

```sh
ngrok http 80
```

For details on how to fully leverage `ngrok` from the command line,
see [`ngrok`'s official documentation](https://ngrok.com/docs/agent/cli/).

## Documentation

For more advanced usage, `pyngrok`'s official documentation is available
at [http://pyngrok.readthedocs.io](http://pyngrok.readthedocs.io).

### `ngrok` Version Compatibility

`pyngrok` is compatible with `ngrok` v2 and v3, but by default it will install v3. To install v2 instead,
[set `ngrok_version` to "v2" in
`PyngrokConfig`](https://pyngrok.readthedocs.io/en/latest/index.html#ngrok-version-compatibility).

## Contributing

If you would like to get involved, be sure to review
the [Contribution Guide](https://github.com/alexdlaird/pyngrok/blob/main/CONTRIBUTING.rst).

Want to contribute financially? If you've found `pyngrok` useful, [sponsorship](https://github.com/sponsors/alexdlaird)
would also be greatly appreciated!
